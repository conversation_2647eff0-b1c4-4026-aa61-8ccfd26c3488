#!/usr/bin/env python3
"""
简单测试重构后的DataHandler类
"""

import pandas as pd
import numpy as np

# 测试导入
try:
    from pyqlab.data.dataset.handler import (
        DataConfig, 
        LabelGenerator, 
        LabelThresholds,
        DirectionType
    )
    print("✓ 成功导入重构后的类")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

# 测试DataConfig
print("\n测试DataConfig...")
config = DataConfig(win=20, step=2)
print(f"✓ DataConfig创建成功: win={config.win}, step={config.step}")

# 测试LabelThresholds
print("\n测试LabelThresholds...")
thresholds = LabelThresholds(long_threshold=0.3, short_threshold=-0.3)
print(f"✓ LabelThresholds创建成功: long={thresholds.long_threshold}, short={thresholds.short_threshold}")

# 测试LabelGenerator
print("\n测试LabelGenerator...")
generator = LabelGenerator(thresholds)
test_data = pd.DataFrame({
    'change': [-0.5, -0.1, 0.1, 0.4],
    'BAR_LENGTH_2': [1, 2, 3, 4]
})
result = generator.generate_labels(test_data)
print(f"✓ LabelGenerator工作正常，生成了{len(result.columns)}列")

# 测试DirectionType
print("\n测试DirectionType...")
direction = DirectionType.LONG
print(f"✓ DirectionType工作正常: {direction.value}")

print("\n🎉 所有基本测试通过！重构成功！")
