# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
"""
重构后的数据处理模块，将原来的DataHandler拆分为多个专注的类
"""

import warnings
import logging
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Union, Tuple, List, Optional, Any
import os
import pandas as pd
import numpy as np
import json
from copy import deepcopy
from functools import partial

from pyqlab.utils import init_instance_by_config
from pyqlab.data.dataset.loader import AHFDataLoader
from pyqlab.const import (
    ALL_FACTOR_NAMES,
    TWO_VAL_FACTOR_NAMES,
    SNAPSHOT_CONTEXT,
    MODEL_FUT_CODES,
    CATEGORY_FACTOR_NAMES,
    CATEGORY_FACTOR_NUMS
)
import pyqlab.data.dataset.loader as data_loader_module
from pyqlab.utils.timefeatures import time_features

warnings.filterwarnings("ignore")

# 配置日志
logger = logging.getLogger(__name__)


class DirectionType(Enum):
    """交易方向枚举"""
    LONG = "long"
    SHORT = "short"
    LONG_SHORT = "ls"
    MULTI_LABEL = "mls"


@dataclass
class LabelThresholds:
    """标签生成阈值配置"""
    long_threshold: float = 0.2
    short_threshold: float = -0.2
    multi_label_positive: float = 0.25
    multi_label_negative: float = -0.25


@dataclass
class DataConfig:
    """数据处理配置类"""
    # 时间范围
    years: Optional[List[str]] = None
    start_date: str = ""
    end_date: str = ""

    # 窗口和采样参数
    win: int = 10
    step: int = 1
    filter_win: int = 0

    # 数据处理选项
    is_filter_extreme: bool = False
    is_normal: bool = True
    fetch_orig: bool = True
    verbose: bool = False

    # 时间编码
    timeenc: Optional[int] = None

    # 因子选择
    sel_fd_names: List[str] = field(default_factory=list)

    # 主要因子数据名称
    main_fd_name: str = "fd_1_0"

    # 标签阈值
    label_thresholds: LabelThresholds = field(default_factory=LabelThresholds)

    # 极端值过滤阈值
    extreme_threshold: float = 3.0

    def __post_init__(self):
        """初始化后的验证"""
        if self.win < 0:
            raise ValueError(f"win must be non-negative, got {self.win}")
        if self.step <= 0:
            raise ValueError(f"step must be positive, got {self.step}")
        if self.filter_win < 0:
            raise ValueError(f"filter_win must be non-negative, got {self.filter_win}")


class LabelGenerator:
    """标签生成器"""

    def __init__(self, thresholds: LabelThresholds):
        self.thresholds = thresholds

    def generate_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成各种类型的标签"""
        result_df = df.copy()

        # 生成多头标签
        result_df['long_label'] = (
            df['change'] > self.thresholds.long_threshold
        ).astype(int)

        # 生成空头标签
        result_df['short_label'] = (
            df['change'] < self.thresholds.short_threshold
        ).astype(int)

        # 生成多分类标签
        conditions = [
            df['change'] < self.thresholds.multi_label_negative,
            df['change'] > self.thresholds.multi_label_positive
        ]
        choices = [0, 2]
        result_df['label'] = np.select(conditions, choices, default=1)

        # 添加bar_length特征
        if 'BAR_LENGTH_2' in df.columns:
            result_df['bar_length'] = df['BAR_LENGTH_2']
        else:
            result_df['bar_length'] = 0

        return result_df

class FeatureProcessor:
    """特征处理器"""

    def __init__(self, config: DataConfig):
        self.config = config

    def get_factor_columns(self, is_all: bool = False) -> List[str]:
        """获取因子列名称"""
        col_names = []
        sel_names = ALL_FACTOR_NAMES if is_all else self.config.sel_fd_names

        for name in sel_names:
            if name in TWO_VAL_FACTOR_NAMES:
                col_names.extend([f"{name}_1", f"{name}_2"])
            else:
                col_names.append(f"{name}_2")

        return col_names

    def select_factor(self, factor_index: int) -> int:
        """选择因子"""
        if not self.config.sel_fd_names:
            return 0

        factor_name = ALL_FACTOR_NAMES[factor_index]
        if factor_name in self.config.sel_fd_names:
            return 2 if factor_name in TWO_VAL_FACTOR_NAMES else 1
        return 0

    def get_selected_factor_names(self, selection_list: List[int]) -> List[str]:
        """根据选择列表获取因子名称"""
        if not selection_list:
            return []

        return [
            ALL_FACTOR_NAMES[i]
            for i, selected in enumerate(selection_list)
            if selected > 0
        ]


class DataNormalizer:
    """数据归一化器"""

    def __init__(self, config: DataConfig):
        self.config = config
        self.fd_means: Dict[str, pd.DataFrame] = {}
        self.fd_stds: Dict[str, pd.DataFrame] = {}

    def load_normalization_stats(self, data_loader: AHFDataLoader):
        """加载归一化统计数据"""
        fd_names = data_loader.get_fd_names()

        for fd_name in fd_names:
            for stat_type in ["mean", "std"]:
                file_path = f'{data_loader.data_path}/ffs_{fd_name}_{stat_type}.csv'

                if not os.path.isfile(file_path):
                    raise FileNotFoundError(f"统计文件未找到: {file_path}")

                stat_data = pd.read_csv(file_path, index_col=0)

                if stat_type == "mean":
                    self.fd_means[fd_name] = stat_data
                else:
                    self.fd_stds[fd_name] = stat_data

    def standardize_group(self, group: pd.DataFrame, means: pd.Series, stds: pd.Series) -> pd.DataFrame:
        """对分组数据进行标准化"""
        code = group.name
        mean = means.loc[code]
        std = stds.loc[code]
        return (group - mean) / std

    def normalize_data(self, fd_dfs: Dict[str, pd.DataFrame], col_names: List[str]) -> Dict[str, pd.DataFrame]:
        """归一化数据"""
        if not col_names or not self.config.is_normal:
            return fd_dfs

        result_dfs = {}

        for key, df in fd_dfs.items():
            if key == self.config.main_fd_name:
                # 主要数据单独处理
                result_dfs[key] = self._normalize_main_data(df, col_names, key)
            else:
                # 其他数据处理
                result_dfs[key] = self._normalize_other_data(df, col_names, key)

        return result_dfs

    def _normalize_main_data(self, df: pd.DataFrame, col_names: List[str], key: str) -> pd.DataFrame:
        """归一化主要数据"""
        if key not in self.fd_means or key not in self.fd_stds:
            logger.warning(f"缺少{key}的归一化统计数据")
            return df

        df_mean = self.fd_means[key][col_names]
        df_std = self.fd_stds[key][col_names]

        partial_func = partial(self.standardize_group, means=df_mean, stds=df_std)
        df_standardized = df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
        df_standardized.fillna(0.0, inplace=True)
        df_standardized.reset_index(drop=False, inplace=True)

        df[col_names] = df_standardized[col_names]
        return df

    def _normalize_other_data(self, df: pd.DataFrame, col_names: List[str], key: str) -> pd.DataFrame:
        """归一化其他数据"""
        if key not in self.fd_means or key not in self.fd_stds:
            logger.warning(f"缺少{key}的归一化统计数据")
            return df[['code', 'date'] + col_names]

        df_mean = self.fd_means[key][col_names]
        df_std = self.fd_stds[key][col_names]

        partial_func = partial(self.standardize_group, means=df_mean, stds=df_std)
        df_standardized = df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
        df_standardized.fillna(0.0, inplace=True)
        df_standardized.reset_index(drop=False, inplace=True)

        df[col_names] = df_standardized[col_names]
        return df[['code', 'date'] + col_names]


class TimeFeatureGenerator:
    """时间特征生成器"""

    def __init__(self, config: DataConfig):
        self.config = config

    def generate_time_features(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """生成时间特征"""
        # 将timestamp转换为东8区日期时间
        df['date'] = pd.to_datetime(df['date'], unit='s') + pd.Timedelta(hours=8)

        # 生成时间特征
        tf_columns = ['tf0', 'tf1', 'tf2', 'tf3', 'tf4']
        df_stamp = time_features(pd.to_datetime(df['date'].values), freq='t').transpose(1, 0)
        df_tf = pd.DataFrame(df_stamp, columns=tf_columns)

        # 合并时间特征
        result_df = pd.concat([df, df_tf], axis=1)

        return result_df, tf_columns


class DataHandler():
    """
    重构后的数据处理器 - 主要负责协调各个组件
    原则：按需读取数据，尽量减少内存占用
    """

    def __init__(
        self,
        years: Optional[List[str]] = None,
        start_date: str = '',
        end_date: str = '',
        data_loader: Union[dict, str, AHFDataLoader] = None,
        win: int = 10,
        step: int = 1,
        filter_win: int = 0,
        is_filter_extreme: bool = False,
        is_normal: bool = True,
        init_data: bool = False,
        fetch_orig: bool = True,
        verbose: bool = False,
        timeenc: Optional[int] = None,
        sel_fd_names: Optional[List[str]] = None,
        main_fd_name: str = 'fd_1_0',
        label_thresholds: Optional[LabelThresholds] = None,
        extreme_threshold: float = 3.0,
        **kwargs
    ):
        # 创建配置对象
        self.config = DataConfig(
            years=years,
            start_date=start_date,
            end_date=end_date,
            win=win,
            step=step,
            filter_win=filter_win,
            is_filter_extreme=is_filter_extreme,
            is_normal=is_normal,
            fetch_orig=fetch_orig,
            verbose=verbose,
            timeenc=timeenc,
            sel_fd_names=sel_fd_names or [],
            main_fd_name=main_fd_name,
            label_thresholds=label_thresholds or LabelThresholds(),
            extreme_threshold=extreme_threshold
        )

        # 初始化组件
        self.feature_processor = FeatureProcessor(self.config)
        self.label_generator = LabelGenerator(self.config.label_thresholds)
        self.normalizer = DataNormalizer(self.config)
        self.time_feature_generator = TimeFeatureGenerator(self.config)

        # 兼容性属性（保持向后兼容）
        self._setup_compatibility_attributes()

        # 数据存储
        self.lb_df = pd.DataFrame()
        self.ft_df = pd.DataFrame()
        self.feat_names = []

        # 设置数据加载器
        if data_loader is None:
            raise ValueError("data_loader参数不能为None")

        self.data_loader = init_instance_by_config(
            data_loader,
            None if (isinstance(data_loader, dict) and "module_path" in data_loader) else data_loader_module,
            accept_types=AHFDataLoader,
        )

        if init_data:
            self.setup_data()

    def _setup_compatibility_attributes(self):
        """设置向后兼容的属性"""
        self.win = self.config.win
        self.step = self.config.step
        self.filter_win = self.config.filter_win
        self.is_filter_extreme = self.config.is_filter_extreme
        self.is_normal = self.config.is_normal
        self.verbose = self.config.verbose
        self.timeenc = self.config.timeenc
        self.start_date = self.config.start_date
        self.end_date = self.config.end_date
        self.years = self.config.years
        self.main_fd_name = self.config.main_fd_name
        self.sel_fd_names = self.config.sel_fd_names

        # 其他兼容性属性
        self.x_data = []
        self.y_data = []
        self.encoded_data = []
        self.direct = 'long'
        self.data_path = ''
        self.ins_nums = tuple()
        self.fetch_orig = self.config.fetch_orig

        # 归一化统计数据的兼容性访问
        self.fd_means = self.normalizer.fd_means
        self.fd_stds = self.normalizer.fd_stds

    def standardize(self, group, means, stds):
        """向后兼容的标准化方法"""
        return self.normalizer.standardize_group(group, means, stds)
    
    def config(self, **kwargs):
        """配置方法 - 重构后更加清晰和安全"""
        if "data_loader" in kwargs and isinstance(self.data_loader, AHFDataLoader):
            kwargs_copy = deepcopy(kwargs)
            kwargs_copy.pop("data_loader", None)

            # 更新配置
            if "kwargs" in kwargs_copy:
                config_updates = kwargs_copy["kwargs"]
                self._update_config_safely(config_updates)
                kwargs_copy.pop("kwargs")

        # 加载归一化统计数据
        try:
            self.normalizer.load_normalization_stats(self.data_loader)
        except FileNotFoundError as e:
            logger.error(f"加载归一化统计数据失败: {e}")
            raise

    def _update_config_safely(self, config_updates: dict):
        """安全地更新配置"""
        # 定义允许更新的属性映射
        allowed_updates = {
            "win": "win",
            "step": "step",
            "direct": "direct",
            "sel_fd_names": "sel_fd_names",
            "filter_win": "filter_win",
            "is_filter_extreme": "is_filter_extreme",
            "is_normal": "is_normal",
            "verbose": "verbose",
            "timeenc": "timeenc",
        }

        for key, value in config_updates.items():
            if key in allowed_updates:
                config_attr = allowed_updates[key]

                # 更新配置对象
                if hasattr(self.config, config_attr):
                    setattr(self.config, config_attr, value)

                # 更新兼容性属性
                setattr(self, key, value)

                logger.debug(f"更新配置: {key} = {value}")
            else:
                logger.warning(f"忽略未知配置项: {key}")

    def _factor_select(self, n: int) -> int:
        """向后兼容的因子选择方法"""
        return self.feature_processor.select_factor(n)

    def _factor_select_name(self, sel_list: List[int]) -> List[str]:
        """向后兼容的因子名称选择方法"""
        return self.feature_processor.get_selected_factor_names(sel_list)

    def _get_factor_cols(self, is_all: bool = False) -> List[str]:
        """向后兼容的因子列获取方法"""
        return self.feature_processor.get_factor_columns(is_all)
    
    def _recover_factor_cols(self):
        """重新生成因子名称，以保证因子顺序与系统一致"""
        f_sel = {}
        f_sel['factor'] = [self._factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]

        self.sel_fd_names = self._factor_select_name(f_sel['factor'])
        self.config.sel_fd_names = self.sel_fd_names

        # 计算实例数量（这里需要根据实际业务逻辑调整）
        factor_count = sum(f_sel['factor'])
        self.ins_nums = (0, factor_count, 0, 0)  # 简化版本，可根据需要调整

    def _get_ins_nums(self) -> tuple:
        """获取实例数量"""
        return self.ins_nums
    
    def _dump_input_param_json(
        self,
        save_path: Optional[str] = None,
        model_type: int = 0,
        seq_len: int = 0,
        label_len: int = 0,
        pred_len: int = 0
    ) -> str:
        """
        生成模型输入参数配置JSON
        注意：两边特征向量的顺序要一致
        """
        f_sel = {
            'model_type': model_type,
            'seq_len': seq_len,
            'label_len': label_len,
            'pred_len': pred_len,
            'timeenc': self.config.timeenc,
            'codes': MODEL_FUT_CODES,
            'factor': [self._factor_select(n) for n in range(len(ALL_FACTOR_NAMES))],
        }

        # 重新生成因子名称，以保证因子顺序与系统一致
        self.sel_fd_names = self._factor_select_name(f_sel['factor'])
        self.config.sel_fd_names = self.sel_fd_names

        all_col_names = self._get_factor_cols(is_all=True)
        col_names = self._get_factor_cols()
        f_sel['fd_sel_index'] = [all_col_names.index(name) for name in col_names]
        f_sel['fd_len'] = sum(f_sel['factor'])

        assert f_sel['fd_len'] == len(f_sel['fd_sel_index']), "因子长度不匹配"

        f_sel["input_dim"] = 1 if self.config.win == 0 else len(self.data_loader.get_fd_names())
        f_sel["code_encoding"] = 2  # 1.onehot 2.标签编码
        f_sel["win"] = self.config.win
        f_sel["filter_win"] = self.config.filter_win
        f_sel["step"] = self.config.step

        if save_path is not None:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(f_sel, f, ensure_ascii=False, indent=2)

        return json.dumps(f_sel, ensure_ascii=False)

    def setup_data(self, enable_cache: bool = False):
        """
        重构后的数据设置方法 - 更加模块化和清晰
        """
        if not isinstance(self.data_loader, AHFDataLoader):
            raise ValueError("data_loader必须是AHFDataLoader类型")

        # 1. 准备因子配置
        self._recover_factor_cols()

        # 2. 加载和预处理原始数据
        fd_dfs = self._load_and_preprocess_data(enable_cache)

        # 3. 验证主要数据存在
        if self.config.main_fd_name not in fd_dfs:
            raise ValueError(f"主要数据 {self.config.main_fd_name} 不存在于数据中: {list(fd_dfs.keys())}")

        # 4. 处理主要数据框
        main_df = self._process_main_dataframe(fd_dfs[self.config.main_fd_name])
        fd_dfs[self.config.main_fd_name] = main_df

        # 5. 归一化数据
        col_names = self._get_factor_cols()
        if col_names:
            fd_dfs = self.normalizer.normalize_data(fd_dfs, col_names)

        # 6. 生成时间特征
        main_df_with_time, tf_columns = self.time_feature_generator.generate_time_features(
            fd_dfs[self.config.main_fd_name]
        )

        # 7. 分离标签和特征数据
        self.lb_df, self.ft_df = self._separate_labels_and_features(
            main_df_with_time, fd_dfs, col_names, tf_columns
        )

        # 8. 设置特征名称
        self.feat_names = self.ft_df.columns.tolist()

        # 9. 输出调试信息
        if self.config.verbose:
            self._print_debug_info(fd_dfs)

    def _load_and_preprocess_data(self, enable_cache: bool) -> Dict[str, pd.DataFrame]:
        """加载和预处理原始数据"""
        # 加载数据
        fd_dfs = self.data_loader.load(self.config.years)

        if not enable_cache:
            self.data_loader.clear_data()

        # 时间范围过滤
        if self.config.start_date and self.config.end_date and self.config.start_date < self.config.end_date:
            fd_dfs = self._filter_by_date_range(fd_dfs)

        # 数据清洗
        fd_dfs = self._clean_data(fd_dfs)

        return fd_dfs

    def _filter_by_date_range(self, fd_dfs: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """按日期范围过滤数据"""
        start_timestamp = int(pd.Timestamp(self.config.start_date).timestamp())
        end_timestamp = int(pd.Timestamp(self.config.end_date).timestamp())

        for key, df in fd_dfs.items():
            # 过滤日期范围
            mask = (df['date'] >= start_timestamp) & (df['date'] <= end_timestamp)
            fd_dfs[key] = df[mask].copy()

            # 排序
            fd_dfs[key].sort_values(by=['code', 'date'], ascending=True, inplace=True)

            if self.config.verbose:
                logger.info(f"日期过滤后 {key}: {fd_dfs[key].shape}")

            # 填充缺失值
            fd_dfs[key].fillna(0.0, inplace=True)

        return fd_dfs

    def _clean_data(self, fd_dfs: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """清洗数据"""
        for key, df in fd_dfs.items():
            # 过滤极端涨跌幅数据
            if 'RSI_2' in df.columns and 'change' in df.columns:
                condition = (
                    (df['RSI_2'] != 0) &
                    (df['change'] <= 7.0) &
                    (df['change'] >= -7.0)
                )
                fd_dfs[key] = df[condition].copy()

                if self.config.verbose:
                    logger.info(f"极端值过滤后 {key}: {fd_dfs[key].shape[0]} 行")

            # 重置索引
            fd_dfs[key].reset_index(drop=True, inplace=True)

            if self.config.verbose:
                logger.info(f"清洗完成 {key}: {fd_dfs[key].shape}")

        return fd_dfs

    def _process_main_dataframe(self, main_df: pd.DataFrame) -> pd.DataFrame:
        """处理主要数据框"""
        # 添加代码编码
        fut_codes_dict = {code: i for i, code in enumerate(MODEL_FUT_CODES)}
        main_df = main_df.copy()
        main_df['code_encoded'] = main_df['code'].apply(lambda x: fut_codes_dict.get(x, 0))

        # 生成标签
        main_df = self.label_generator.generate_labels(main_df)

        return main_df

    def _separate_labels_and_features(
        self,
        main_df_with_time: pd.DataFrame,
        fd_dfs: Dict[str, pd.DataFrame],
        col_names: List[str],
        tf_columns: List[str]
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """分离标签和特征数据"""
        # 准备标签数据
        label_columns = ['code', 'date', 'change', 'long_label', 'short_label', 'label', 'bar_length', 'code_encoded']
        lb_df = main_df_with_time[label_columns + tf_columns].copy()

        # 准备特征数据 - 从主数据中移除标签相关列
        feature_df = main_df_with_time.copy()
        columns_to_drop = ['code', 'date', 'change', 'code_encoded', 'long_label', 'short_label', 'bar_length', 'label'] + tf_columns
        feature_df.drop(columns=columns_to_drop, inplace=True)

        # 更新fd_dfs中的主数据
        fd_dfs[self.config.main_fd_name] = feature_df

        # 从所有数据框中移除code和date列（除了已经处理的主数据）
        for key, df in fd_dfs.items():
            if key != self.config.main_fd_name and 'code' in df.columns:
                df.drop(columns=['code', 'date'], inplace=True, errors='ignore')

        # 合并所有特征数据
        feature_dfs = [df for df in fd_dfs.values() if not df.empty]
        if feature_dfs:
            ft_df = pd.concat(feature_dfs, axis=1)
        else:
            ft_df = feature_df

        return lb_df, ft_df

    def _print_debug_info(self, fd_dfs: Dict[str, pd.DataFrame]):
        """打印调试信息"""
        logger.info("=== 数据形状信息 ===")
        for key, df in fd_dfs.items():
            logger.info(f"{key}: {df.shape}")

        logger.info(f"特征列: {self.ft_df.columns.tolist()}")
        logger.info(f"标签数据形状: {self.lb_df.shape}, 特征数据形状: {self.ft_df.shape}")

        if not self.lb_df.empty:
            logger.info("标签分布:")
            logger.info(f"多头标签: {self.lb_df['long_label'].value_counts().to_dict()}")
            logger.info(f"空头标签: {self.lb_df['short_label'].value_counts().to_dict()}")
            logger.info(f"多分类标签: {self.lb_df['label'].value_counts().to_dict()}")


class DataSampler:
    """数据采样器"""

    def __init__(self, config: DataConfig):
        self.config = config

    def sample_data(
        self,
        ft_df: pd.DataFrame,
        lb_df: pd.DataFrame,
        direction: DirectionType,
        win: int,
        filter_win: int
    ) -> Union[Tuple[np.ndarray, np.ndarray, np.ndarray],
               Tuple[np.ndarray, np.ndarray, np.ndarray, List, List]]:
        """采样数据"""

        # 验证参数
        if win <= 0:
            raise ValueError(f"窗口大小必须大于0，当前值: {win}")

        # 准备数据
        feature_data = ft_df.astype(np.float32).values
        label_data = self._get_label_data(lb_df, direction)
        embedding_data = self._get_embedding_data(lb_df)
        time_data = self._get_time_data(lb_df) if self.config.timeenc is not None else None

        if self.config.verbose:
            logger.info(f"分类列: {self.config.ct_cat_cols_names}")
            logger.info(f"特征数据形状: {feature_data.shape}, 标签数据形状: {label_data.shape}, 嵌入数据形状: {embedding_data.shape}")

        # 采样
        if filter_win > 1:
            return self._sample_with_filter(
                feature_data, label_data, embedding_data, time_data, lb_df, win, filter_win
            )
        else:
            return self._sample_regular(
                feature_data, label_data, embedding_data, time_data, win
            )

    def _get_label_data(self, lb_df: pd.DataFrame, direction: DirectionType) -> np.ndarray:
        """获取标签数据"""
        if direction == DirectionType.LONG:
            return lb_df['long_label'].values
        elif direction == DirectionType.SHORT:
            return lb_df['short_label'].values
        elif direction == DirectionType.MULTI_LABEL:
            return lb_df['label'].values
        elif direction == DirectionType.LONG_SHORT:
            return lb_df['change'].values
        else:
            raise ValueError(f"不支持的方向类型: {direction}")

    def _get_embedding_data(self, lb_df: pd.DataFrame) -> np.ndarray:
        """获取嵌入数据"""
        embed_columns = ['code_encoded'] + self.config.ct_cat_cols_names
        return lb_df[embed_columns].astype(np.int32).values

    def _get_time_data(self, lb_df: pd.DataFrame) -> np.ndarray:
        """获取时间数据"""
        time_columns = ['tf0', 'tf1', 'tf2', 'tf3', 'tf4']
        if self.config.timeenc == 0:
            return lb_df[time_columns].astype(np.int32).values
        elif self.config.timeenc == 1:
            return lb_df[time_columns].astype(np.float32).values
        else:
            return lb_df[time_columns].astype(np.float32).values

    def _sample_with_filter(
        self,
        feature_data: np.ndarray,
        label_data: np.ndarray,
        embedding_data: np.ndarray,
        time_data: Optional[np.ndarray],
        lb_df: pd.DataFrame,
        win: int,
        filter_win: int
    ) -> Union[Tuple[np.ndarray, np.ndarray, np.ndarray],
               Tuple[np.ndarray, np.ndarray, np.ndarray, List, List]]:
        """使用过滤窗口采样"""
        x_data, y_data, emb_data = [], [], []
        x_mark, y_mark = [], []
        filter_count = 0

        # 计算滚动最大值索引
        rolling_max_index = lb_df['change'].rolling(filter_win).apply(lambda x: abs(x).idxmax())
        rolling_max_index = rolling_max_index.fillna(0).astype(int)

        for i in range(win, len(rolling_max_index)):
            if rolling_max_index[i] == lb_df.index[i]:
                # 检查是否跨代码
                if win > 0 and embedding_data[i][0] != embedding_data[i - win][0]:
                    continue

                # 过滤极端值
                if self.config.is_filter_extreme:
                    if self._is_extreme_value(feature_data, i, win):
                        filter_count += 1
                        continue

                x_data.append(feature_data[i - win:i])
                y_data.append(label_data[i])
                emb_data.append(embedding_data[i - win:i].tolist())

                if time_data is not None:
                    x_mark.append(time_data[i - win:i])
                    y_mark.append(time_data[i])

        self._print_sampling_stats(y_data, filter_count, filter_win)

        if time_data is not None:
            return np.array(emb_data), np.array(x_data), np.array(y_data), x_mark, y_mark
        else:
            return np.array(emb_data), np.array(x_data), np.array(y_data)

    def _sample_regular(
        self,
        feature_data: np.ndarray,
        label_data: np.ndarray,
        embedding_data: np.ndarray,
        time_data: Optional[np.ndarray],
        win: int
    ) -> Union[Tuple[np.ndarray, np.ndarray, np.ndarray],
               Tuple[np.ndarray, np.ndarray, np.ndarray, List, List]]:
        """常规采样"""
        x_data, y_data, emb_data = [], [], []
        x_mark, y_mark = [], []
        filter_count = 0

        for i in range(win, len(feature_data), self.config.step):
            # 检查是否跨代码
            if win > 0 and embedding_data[i][0] != embedding_data[i - win][0]:
                continue

            # 过滤极端值
            if self.config.is_filter_extreme:
                if self._is_extreme_value(feature_data, i, win):
                    filter_count += 1
                    continue

            x_data.append(feature_data[i - win:i])
            y_data.append(label_data[i])
            emb_data.append(embedding_data[i - win:i].tolist())

            if time_data is not None:
                x_mark.append(time_data[i - win:i])
                y_mark.append(time_data[i])

        self._print_sampling_stats(y_data, filter_count, 0)

        if time_data is not None:
            return np.array(emb_data), np.array(x_data), np.array(y_data), x_mark, y_mark
        else:
            return np.array(emb_data), np.array(x_data), np.array(y_data)

    def _is_extreme_value(self, feature_data: np.ndarray, i: int, win: int) -> bool:
        """检查是否为极端值"""
        window_data = feature_data[i - win:i, 1]  # 假设第1列是主要特征
        return (
            (window_data > self.config.extreme_threshold).any() or
            (window_data < -self.config.extreme_threshold).any()
        )

    def _print_sampling_stats(self, y_data: List, filter_count: int, filter_win: int):
        """打印采样统计信息"""
        if y_data:
            label_ratio = round(sum(y_data) / len(y_data) * 100, 3)
            logger.info("=" * 30)
            logger.info(f"标签比例: {label_ratio}%, 总数: {len(y_data)}, 过滤窗口: {filter_win}, "
                       f"极端值过滤: {self.config.is_filter_extreme}, 过滤数量: {filter_count}, "
                       f"归一化: {self.config.is_normal}")
            logger.info("=" * 30)


    def fetch(
        self,
        direct: str,
        win: int,
        filter_win: int
    ) -> Union[Tuple[np.ndarray, np.ndarray, np.ndarray],
               Tuple[np.ndarray, np.ndarray, np.ndarray, List, List]]:
        """
        重构后的数据获取方法 - 更加清晰和模块化

        Args:
            direct: 交易方向 ('long', 'short', 'ls', 'mls')
            win: 窗口大小
            filter_win: 过滤窗口大小

        Returns:
            根据timeenc设置返回不同格式的数据
        """
        # 更新配置
        self.direct = direct
        self.config.win = win
        self.config.filter_win = filter_win
        self.win = win  # 向后兼容
        self.filter_win = filter_win  # 向后兼容

        # 验证方向参数
        try:
            direction = DirectionType(direct)
        except ValueError:
            raise ValueError(f"不支持的交易方向: {direct}. 支持的方向: {[d.value for d in DirectionType]}")

        if win <= 0:
            raise ValueError(f"窗口大小必须大于0，当前值: {win}")

        # 生成模型输入数据配置文件
        self._dump_input_param_json()

        # 检查数据是否已准备好
        if self.ft_df.empty or self.lb_df.empty:
            raise ValueError("数据未准备好，请先调用setup_data()方法")

        # 创建采样器并采样数据
        sampler = DataSampler(self.config)
        return sampler.sample_data(self.ft_df, self.lb_df, direction, win, filter_win)


# 功能测试
if __name__ == "__main__":
    loader = AHFDataLoader(
        data_path="f:/featdata/tmp",
        train_codes=[],
        years=['2025'],
        fd_set = set({(1,0), (1,1)})
    )
    dh = DataHandler(
        start_date="2020-01-01",
        end_date="2020-12-31",
        data_loader=loader,
        win=10,
        step=1,
        filter_win=0,
        is_filter_extreme=False,
        is_normal=True,
        fetch_orig=True,
        verbose=True,
    )
    dh.setup_data()
    x_data, y_data, emb_data, x_mark, y_mark = dh.fetch("long", 10, 0)
