#!/usr/bin/env python3
"""
测试参数传递是否正确
"""

def test_parameter_passing():
    """测试参数传递"""
    print("测试参数传递...")
    
    try:
        from pyqlab.data.dataset.handler import <PERSON>Handler, DataConfig, LabelThresholds
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 测试1: 直接传递AHFDataLoader实例
        print("\n1. 测试直接传递AHFDataLoader实例")
        loader = AHFDataLoader(
            data_path="f:/featdata/tmp",
            train_codes=["TEST"],
            years=["2025"],
            fd_set={(1,0), (1,1)}
        )
        
        handler1 = DataHandler(
            data_loader=loader,
            win=5,
            step=1,
            sel_fd_names=["RSI", "MACD"],
            sel_ct_names=["context1"],
            ct_cat_cols_names=["cat1"],
            ct_cat_num_embeds=[32],
            is_normal=False,
            verbose=False
        )
        
        # 验证参数传递
        assert handler1.config.sel_fd_names == ["RSI", "MACD"]
        assert handler1.config.sel_ct_names == ["context1"]
        assert handler1.config.ct_cat_cols_names == ["cat1"]
        assert handler1.config.ct_cat_num_embeds == [32]
        assert handler1.sel_fd_names == ["RSI", "MACD"]
        assert handler1.sel_ct_names == ["context1"]
        assert handler1.ct_cat_cols_names == ["cat1"]
        assert handler1.ct_cat_num_embeds == [32]
        
        print("✓ 直接传递AHFDataLoader实例测试通过")
        
        # 测试2: 传递配置字典
        print("\n2. 测试传递配置字典")
        loader_config = {
            "class": "AHFDataLoader",
            "kwargs": {
                "data_path": "f:/featdata/tmp",
                "train_codes": ["TEST"],
                "years": ["2025"],
                "fd_set": {(1,0), (1,1)}
            }
        }
        
        handler2 = DataHandler(
            data_loader=loader_config,
            win=10,
            step=2,
            sel_fd_names=["VOLUME", "PRICE"],
            is_normal=False,
            verbose=False
        )
        
        # 验证参数传递
        assert handler2.config.win == 10
        assert handler2.config.step == 2
        assert handler2.config.sel_fd_names == ["VOLUME", "PRICE"]
        assert handler2.win == 10
        assert handler2.step == 2
        assert handler2.sel_fd_names == ["VOLUME", "PRICE"]
        
        print("✓ 传递配置字典测试通过")
        
        # 测试3: 测试配置更新
        print("\n3. 测试配置更新")
        handler1.config.sel_fd_names = ["NEW_FACTOR"]
        handler1.sel_fd_names = ["NEW_FACTOR"]  # 同步更新兼容性属性
        
        assert handler1.config.sel_fd_names == ["NEW_FACTOR"]
        assert handler1.sel_fd_names == ["NEW_FACTOR"]
        
        print("✓ 配置更新测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数传递测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n测试向后兼容性...")
    
    try:
        from pyqlab.data.dataset.handler import DataHandler
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 使用原有的方式创建
        loader = AHFDataLoader()
        handler = DataHandler(
            years=["2025"],
            start_date="2025-01-01",
            end_date="2025-12-31",
            data_loader=loader,
            win=10,
            step=1,
            filter_win=0,
            is_filter_extreme=False,
            is_normal=False,  # 关闭归一化避免需要文件
            fetch_orig=True,
            verbose=False,
        )
        
        # 验证所有原有属性都存在
        required_attrs = [
            'win', 'step', 'filter_win', 'is_filter_extreme', 'is_normal',
            'verbose', 'start_date', 'end_date', 'years', 'data_loader',
            'sel_fd_names', 'sel_ct_names', 'ct_cat_cols_names', 'ct_cat_num_embeds'
        ]
        
        for attr in required_attrs:
            assert hasattr(handler, attr), f"缺少属性: {attr}"
        
        # 验证方法存在
        required_methods = [
            'setup_data', 'fetch', 'config', '_get_factor_cols', 'standardize'
        ]
        
        for method in required_methods:
            assert hasattr(handler, method), f"缺少方法: {method}"
        
        print("✓ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始测试参数传递和向后兼容性...")
    print("=" * 50)
    
    success = True
    success &= test_parameter_passing()
    success &= test_backward_compatibility()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！参数传递和向后兼容性都正常！")
    else:
        print("❌ 部分测试失败")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
