#!/usr/bin/env python3
"""
测试重构后的DataHandler类
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from pyqlab.data.dataset.handler import (
    DataHandler, 
    DataConfig, 
    LabelGenerator, 
    LabelThresholds,
    FeatureProcessor,
    DataNormalizer,
    TimeFeatureGenerator,
    DataSampler,
    DirectionType
)

def test_data_config():
    """测试DataConfig类"""
    print("测试DataConfig类...")
    
    # 测试默认配置
    config = DataConfig()
    assert config.win == 10
    assert config.step == 1
    assert config.is_normal == True
    
    # 测试自定义配置
    config = DataConfig(
        win=20,
        step=2,
        is_filter_extreme=True,
        extreme_threshold=2.5
    )
    assert config.win == 20
    assert config.step == 2
    assert config.is_filter_extreme == True
    assert config.extreme_threshold == 2.5
    
    print("✓ DataConfig测试通过")

def test_label_generator():
    """测试LabelGenerator类"""
    print("测试LabelGenerator类...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'change': [-0.5, -0.1, 0.1, 0.3, 0.5],
        'BAR_LENGTH_2': [1, 2, 3, 4, 5]
    })
    
    # 测试标签生成
    thresholds = LabelThresholds(
        long_threshold=0.2,
        short_threshold=-0.2,
        multi_label_positive=0.25,
        multi_label_negative=-0.25
    )
    
    generator = LabelGenerator(thresholds)
    result = generator.generate_labels(test_data)
    
    # 验证结果
    assert 'long_label' in result.columns
    assert 'short_label' in result.columns
    assert 'label' in result.columns
    assert 'bar_length' in result.columns
    
    # 验证标签逻辑
    assert result['long_label'].iloc[3] == 1  # change=0.3 > 0.2
    assert result['short_label'].iloc[0] == 1  # change=-0.5 < -0.2
    assert result['label'].iloc[0] == 0  # change=-0.5 < -0.25
    assert result['label'].iloc[4] == 2  # change=0.5 > 0.25
    assert result['label'].iloc[2] == 1  # -0.25 <= change=0.1 <= 0.25
    
    print("✓ LabelGenerator测试通过")

def test_feature_processor():
    """测试FeatureProcessor类"""
    print("测试FeatureProcessor类...")
    
    config = DataConfig(sel_fd_names=['RSI', 'MACD', 'VOLUME'])
    processor = FeatureProcessor(config)
    
    # 测试因子列获取
    cols = processor.get_factor_columns()
    expected_cols = ['RSI_1', 'RSI_2', 'MACD_2', 'VOLUME_1', 'VOLUME_2']
    
    # 验证包含预期的列（顺序可能不同）
    for col in expected_cols:
        assert col in cols, f"缺少列: {col}"
    
    print("✓ FeatureProcessor测试通过")

def test_direction_type():
    """测试DirectionType枚举"""
    print("测试DirectionType枚举...")
    
    assert DirectionType.LONG.value == "long"
    assert DirectionType.SHORT.value == "short"
    assert DirectionType.LONG_SHORT.value == "ls"
    assert DirectionType.MULTI_LABEL.value == "mls"
    
    # 测试从字符串创建
    assert DirectionType("long") == DirectionType.LONG
    assert DirectionType("mls") == DirectionType.MULTI_LABEL
    
    print("✓ DirectionType测试通过")

def test_time_feature_generator():
    """测试TimeFeatureGenerator类"""
    print("测试TimeFeatureGenerator类...")
    
    config = DataConfig()
    generator = TimeFeatureGenerator(config)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'date': [1640995200, 1640995260, 1640995320],  # 2022-01-01的时间戳
        'other_col': [1, 2, 3]
    })
    
    result_df, tf_columns = generator.generate_time_features(test_data)
    
    # 验证时间特征列
    assert len(tf_columns) == 5
    assert all(col in result_df.columns for col in tf_columns)
    assert all(col.startswith('tf') for col in tf_columns)
    
    print("✓ TimeFeatureGenerator测试通过")

def main():
    """运行所有测试"""
    print("开始测试重构后的DataHandler组件...")
    print("=" * 50)
    
    try:
        test_data_config()
        test_label_generator()
        test_feature_processor()
        test_direction_type()
        test_time_feature_generator()
        
        print("=" * 50)
        print("✅ 所有测试通过！重构成功！")
        
    except Exception as e:
        print("=" * 50)
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
