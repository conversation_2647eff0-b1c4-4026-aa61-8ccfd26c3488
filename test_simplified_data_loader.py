#!/usr/bin/env python3
"""
测试简化后的数据加载器创建逻辑
"""

def test_simplified_data_loader_creation():
    """测试简化后的数据加载器创建"""
    print("测试简化后的数据加载器创建...")
    
    try:
        from pyqlab.data.dataset.handler import DataHandler
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 测试1: 直接传递AHFDataLoader实例
        print("\n1. 测试直接传递AHFDataLoader实例")
        loader = AHFDataLoader(
            data_path="f:/featdata/tmp",
            train_codes=["TEST1", "TEST2"],
            years=["2024", "2025"],
            fd_set={(1,0), (1,1), (2,0)}
        )
        
        handler1 = DataHandler(
            data_loader=loader,
            years=["2023"],  # 这个应该不会覆盖loader中的years
            data_path="different/path",  # 这个也不会覆盖
            verbose=False
        )
        
        # 验证loader保持原有配置
        assert handler1.data_loader.years == ["2024", "2025"], f"Expected ['2024', '2025'], got {handler1.data_loader.years}"
        assert handler1.data_loader.data_path == "f:/featdata/tmp", f"Expected 'f:/featdata/tmp', got {handler1.data_loader.data_path}"
        assert handler1.data_loader.train_codes == ["TEST1", "TEST2"]
        assert len(handler1.data_loader.fd_set) == 3
        
        print("✓ 直接传递AHFDataLoader实例测试通过")
        
        # 测试2: 传递配置字典，使用DataHandler的参数
        print("\n2. 测试传递配置字典，使用DataHandler的参数")
        loader_config = {
            "train_codes": ["CONFIG_TEST"],
            "fd_set": {(1,0), (2,0)}
        }
        
        handler2 = DataHandler(
            data_loader=loader_config,
            years=["2025"],
            data_path="f:/featdata/tmp",
            verbose=False
        )
        
        # 验证DataHandler的参数被正确传递给loader
        assert handler2.data_loader.years == ["2025"], f"Expected ['2025'], got {handler2.data_loader.years}"
        assert handler2.data_loader.data_path == "f:/featdata/tmp", f"Expected 'f:/featdata/tmp', got {handler2.data_loader.data_path}"
        assert handler2.data_loader.train_codes == ["CONFIG_TEST"]  # 这个来自配置字典
        assert len(handler2.data_loader.fd_set) == 2
        
        print("✓ 传递配置字典测试通过")
        
        # 测试3: 配置字典中的参数优先级
        print("\n3. 测试配置字典中的参数优先级")
        loader_config_with_override = {
            "years": ["2026"],  # 这个应该覆盖DataHandler的years
            "data_path": "override/path",  # 这个应该覆盖DataHandler的data_path
            "train_codes": ["OVERRIDE_TEST"]
        }
        
        handler3 = DataHandler(
            data_loader=loader_config_with_override,
            years=["2025"],  # 应该被覆盖
            data_path="f:/featdata/tmp",  # 应该被覆盖
            verbose=False
        )
        
        # 验证配置字典中的参数优先
        assert handler3.data_loader.years == ["2026"], f"Expected ['2026'], got {handler3.data_loader.years}"
        assert handler3.data_loader.data_path == "override/path", f"Expected 'override/path', got {handler3.data_loader.data_path}"
        assert handler3.data_loader.train_codes == ["OVERRIDE_TEST"]
        
        print("✓ 配置字典参数优先级测试通过")
        
        # 测试4: 空配置字典，完全使用DataHandler参数
        print("\n4. 测试空配置字典，完全使用DataHandler参数")
        empty_loader_config = {}
        
        handler4 = DataHandler(
            data_loader=empty_loader_config,
            years=["2024"],
            data_path="f:/featdata/empty",
            verbose=False
        )
        
        # 验证DataHandler的参数被使用
        assert handler4.data_loader.years == ["2024"], f"Expected ['2024'], got {handler4.data_loader.years}"
        assert handler4.data_loader.data_path == "f:/featdata/empty", f"Expected 'f:/featdata/empty', got {handler4.data_loader.data_path}"
        
        print("✓ 空配置字典测试通过")
        
        # 测试5: 测试错误情况
        print("\n5. 测试错误情况")
        try:
            handler_error = DataHandler(
                data_loader="invalid_type",  # 字符串不再支持
                verbose=False
            )
            assert False, "应该抛出异常"
        except ValueError as e:
            print(f"✓ 正确捕获错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化数据加载器创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n测试向后兼容性...")
    
    try:
        from pyqlab.data.dataset.handler import DataHandler
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 测试原有的使用方式仍然工作
        loader = AHFDataLoader()
        handler = DataHandler(
            data_loader=loader,
            years=["2025"],
            data_path="f:/featdata/test",
            win=10,
            step=1,
            verbose=False
        )
        
        # 验证所有属性都正确设置
        assert handler.data_loader == loader
        assert handler.years == ["2025"]
        assert handler.data_path == "f:/featdata/test"
        assert handler.win == 10
        assert handler.step == 1
        
        # 验证配置对象也正确设置
        assert handler.config.years == ["2025"]
        assert handler.config.data_path == "f:/featdata/test"
        assert handler.config.win == 10
        assert handler.config.step == 1
        
        print("✓ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始测试简化后的数据加载器创建逻辑...")
    print("=" * 60)
    
    success = True
    success &= test_simplified_data_loader_creation()
    success &= test_backward_compatibility()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！简化后的数据加载器创建逻辑工作正常！")
        print("\n✅ 验证结果:")
        print("  - 直接传递AHFDataLoader实例时保持原有配置")
        print("  - 传递配置字典时正确合并DataHandler参数")
        print("  - 配置字典中的参数具有更高优先级")
        print("  - 空配置字典时完全使用DataHandler参数")
        print("  - 移除了复杂的init_instance_by_config逻辑")
        print("  - 保持完全的向后兼容性")
    else:
        print("❌ 部分测试失败")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
