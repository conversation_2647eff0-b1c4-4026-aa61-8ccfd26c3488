#!/usr/bin/env python3
"""
测试修复后的AHFDataLoader和重构后的DataHandler
"""

def test_loader_creation():
    """测试AHFDataLoader创建"""
    print("测试AHFDataLoader创建...")
    
    try:
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 测试默认参数创建
        loader = AHFDataLoader()
        print(f"✓ 默认创建成功，fd_set: {loader.fd_set}")
        
        # 测试自定义参数创建
        custom_fd_set = {(1, 0), (2, 0)}
        loader2 = AHFDataLoader(
            data_path="/test/path",
            train_codes=["A", "B"],
            years=["2023"],
            fd_set=custom_fd_set
        )
        print(f"✓ 自定义创建成功，fd_set: {loader2.fd_set}")
        
        # 验证fd_set类型
        assert isinstance(loader.fd_set, set), "fd_set应该是set类型"
        assert len(loader.fd_set) == 6, "默认fd_set应该有6个元素"
        
        print("✓ AHFDataLoader测试通过")
        return True
        
    except Exception as e:
        print(f"❌ AHFDataLoader测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_handler_creation():
    """测试DataHandler创建"""
    print("\n测试DataHandler创建...")
    
    try:
        from pyqlab.data.dataset.handler import DataHandler
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 创建一个简单的data_loader配置
        loader = AHFDataLoader()
        
        # 测试DataHandler创建
        handler = DataHandler(
            win=5,
            step=1,
            data_loader=loader,
            verbose=True
        )
        
        print(f"✓ DataHandler创建成功")
        print(f"  - win: {handler.win}")
        print(f"  - step: {handler.step}")
        print(f"  - config.win: {handler.config.win}")
        print(f"  - data_loader类型: {type(handler.data_loader).__name__}")
        
        print("✓ DataHandler测试通过")
        return True
        
    except Exception as e:
        print(f"❌ DataHandler测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成功能"""
    print("\n测试集成功能...")
    
    try:
        from pyqlab.data.dataset.handler import DataHandler, DataConfig, LabelThresholds
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 创建配置
        config = DataConfig(
            win=10,
            step=2,
            is_normal=False,  # 关闭归一化以避免需要统计文件
            verbose=True,
            label_thresholds=LabelThresholds(
                long_threshold=0.3,
                short_threshold=-0.3
            )
        )
        
        # 创建loader
        loader = AHFDataLoader(
            data_path="/tmp",  # 使用临时路径
            train_codes=["TEST"],
            years=["2024"]
        )
        
        # 创建handler
        handler = DataHandler(
            data_loader=loader,
            **{k: v for k, v in config.__dict__.items() if k != 'label_thresholds'},
            label_thresholds=config.label_thresholds
        )
        
        print("✓ 集成测试通过 - 所有组件可以正常协作")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始测试修复后的代码...")
    print("=" * 50)
    
    success = True
    success &= test_loader_creation()
    success &= test_handler_creation()
    success &= test_integration()
    
    print("=" * 50)
    if success:
        print("🎉 所有测试通过！修复和重构都成功！")
    else:
        print("❌ 部分测试失败")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
