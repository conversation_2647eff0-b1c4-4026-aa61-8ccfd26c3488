#!/usr/bin/env python3
"""
快速测试重构后的代码
"""

print("开始快速测试...")

# 测试1: 导入
try:
    from pyqlab.data.dataset.handler import DataHandler
    from pyqlab.data.dataset.loader import AHFDataLoader
    print("✓ 导入成功")
except Exception as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

# 测试2: 创建loader
try:
    loader = AHFDataLoader()
    print(f"✓ AHFDataLoader创建成功，fd_set大小: {len(loader.fd_set)}")
except Exception as e:
    print(f"❌ AHFDataLoader创建失败: {e}")
    exit(1)

# 测试3: 创建handler
try:
    handler = DataHandler(
        data_loader=loader,
        win=5,
        step=1,
        is_normal=False,
        verbose=False
    )
    print("✓ DataHandler创建成功")
except Exception as e:
    print(f"❌ DataHandler创建失败: {e}")
    exit(1)

# 测试4: 检查方法
try:
    methods = ['setup_data', 'fetch', 'config']
    for method in methods:
        if hasattr(handler, method):
            print(f"✓ {method} 方法存在")
        else:
            print(f"❌ {method} 方法不存在")
except Exception as e:
    print(f"❌ 方法检查失败: {e}")

print("\n🎉 快速测试完成！")
