# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
"""
Handler对原始数据做处理，按需提供给模型
"""
# coding=utf-8
# import abc
# import bisect
# import logging
# from pathlib import Path
# from sklearn import model_selection
import warnings
from inspect import getfullargspec
from typing import Callable, Dict, Union, Tuple, List, Iterator, Optional
import os
import pandas as pd
import numpy as np
import json
from pprint import pprint
from copy import deepcopy
from sklearn.model_selection import KFold
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号
from pyqlab.utils import init_instance_by_config

from pyqlab.data.dataset.loader import AHFDataLoader
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MODEL_FUT_CODES, CATEGORY_FACTOR_NAMES, CATEGORY_FACTOR_NUMS
import pyqlab.data.dataset.loader as data_loader_module
from pyqlab.utils.timefeatures import time_features
from functools import partial
warnings.filterwarnings("ignore")

class DataHandler():
    """
    加载raw data进行加工处理的地方
    原则：按需读取数据，尽量减少内存占用
    """
    def __init__(
        self,
        years=None,
        start_date='',
        end_date='',
        data_loader: Union[dict, str, AHFDataLoader]=None,
        win = 10,
        step = 1,
        filter_win = 0,
        is_filter_extreme = False,
        is_normal = True,
        init_data=False,
        fetch_orig=True,
        verbose=False,
        timeenc=None,
        **kwargs
    ):
        self.win = win
        self.step = step
        self.filter_win = filter_win
        self.is_filter_extreme = is_filter_extreme # 是否过滤极端值
        self.is_normal = is_normal # 是否归一化
        self.x_data = []
        self.y_data = []
        self.encoded_data = []
        self.direct = 'long'
        self.data_path = ''
        self.sel_fd_names=[]
        self.sel_ct_names=[]
        self.ct_cat_cols_names = []
        self.ct_cat_num_embeds = [64,]
        self.feat_names = []
        self.verbose = verbose
        self.timeenc = timeenc
        self.ins_nums = tuple()
        self.start_date = start_date
        self.end_date = end_date
        self.data_loader = data_loader
        self.years = years
        self.main_fd_name = 'fd_1_0'
        self.fd_means = {}
        self.fd_stds = {}

        
        if data_loader is None:
            raise ValueError("DataHandlerAF init parameter data_loader is None")
        # Setup data loader
        assert data_loader is not None  # to make start_time end_time could have None default value

        # what data source to load data
        self.data_loader = init_instance_by_config(
            data_loader,
            None if (isinstance(data_loader, dict) and "module_path" in data_loader) else data_loader_module,
            accept_types=AHFDataLoader,
        )
        self.fetch_orig = fetch_orig
        if init_data:
            self.setup_data()

        self.lb_df = pd.DataFrame()
        self.ft_df = pd.DataFrame()

    def standardize(self, group, means, stds):
        code = group.name
        mean = means.loc[code]
        std = stds.loc[code]
        group = (group - mean) / std
        return group
    
    def config(self, **kwargs):
        if "data_loader" in kwargs and isinstance(self.data_loader, AHFDataLoader):
            #self.data_loader.config(**kwargs["data_loader"]["kwargs"])
            # print(">>>> data_handler")
            # pprint(kwargs)
            kwargs2 = deepcopy(kwargs)
            kwargs2.pop("data_loader")

            # TODO: 对DataHandler新的参数config进行修改，使其支持直接传入参数
            attr_list = {
                "win", "step", "direct", 
                "sel_lf_names", "sel_sf_names", "sel_mf_names", "sel_ct_names",
                "filter_win", "is_filter_extreme", "is_normal", "verbose",
                "data_path", "timeenc", "ins_nums"}
            for k, v in kwargs2["kwargs"].items():
                if k in attr_list:
                    setattr(self, k, v)
            kwargs2.pop("kwargs")
            # super().config(**kwargs2)

        # 判断文件是否存在
        fd_names = self.data_loader.get_fd_names()
        ext = ["mean", "std"]
        for fd in fd_names:
            for e in ext:
                data_file = f'{self.data_loader.data_path}/ffs_{fd}_{e}.csv'
                if not os.path.isfile(data_file):
                    raise ValueError(f"Data file {data_file} not found")
                if e == "mean":
                    self.fd_means[fd] = pd.read_csv(data_file, index_col=0)
                else:
                    self.fd_stds[fd] = pd.read_csv(data_file, index_col=0)

    def _factor_select(self, n):
        if len(self.sel_fd_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_fd_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _factor_select_name(self, sel_list):
        sel_name=[]
        if len(sel_list) == 0:
            return sel_name
        for n in range(len(ALL_FACTOR_NAMES)):
            if sel_list[n] > 0:
                sel_name.append(ALL_FACTOR_NAMES[n])
        return sel_name

    def _get_factor_cols(self, is_all=False):
        """
        因子列名称
        """
        col_names = []
        sel_names = []
        if is_all:
            sel_names = ALL_FACTOR_NAMES
        else:
            sel_names = self.sel_fd_names

        for name in sel_names:
            if name in TWO_VAL_FACTOR_NAMES:
                col_names.append(f"{name}_1")
                col_names.append(f"{name}_2")
            else:
                col_names.append(f"{name}_2")

        return col_names
    
    def _recover_factor_cols(self):
        # 重新生成因子名称，以保证因子顺序与系统一致 
        f_sel = {}
        f_sel['factor'] = [self._factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]

        self.sel_fd_names = self._factor_select_name(f_sel['factor'])
        self.ins_nums = (sum(f_sel['fd']),
                sum(f_sel['fast']),
                sum(f_sel['main']),
                sum(f_sel['context']) - len(self.ct_cat_cols_names))

    def _get_ins_nums(self):
        return self.ins_nums
    
    def _dump_input_param_json(self, save_path: str = None, model_type=0, seq_len=0, label_len=0, pred_len=0):
        """
        注意：两边特征向量的顺序要一致
        """
        #if self.model_name_suff == "":
        #    return
        f_sel = {}
        f_sel['model_type'] = model_type
        f_sel['seq_len'] = seq_len
        f_sel['label_len'] = label_len
        f_sel['pred_len'] = pred_len
        f_sel['timeenc'] = self.timeenc
        f_sel['codes'] = MODEL_FUT_CODES # sorted(MAIN_FUT_CODES + SF_FUT_CODES)
        f_sel['factor'] = [self._factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        # f_sel['codes'] = sorted(MAIN_FUT_CODES)# sorted(lb_df.code.unique().tolist())

        # 重新生成因子名称，以保证因子顺序与系统一致 
        self.sel_fd_names = self._factor_select_name(f_sel['factor'])

        all_col_names = self._get_factor_cols(is_all=True)
        col_names = self._get_factor_cols()
        f_sel['fd_sel_index'] = [all_col_names.index(name) for name in col_names]
        f_sel['fd_len'] = sum(f_sel['factor'])

        assert f_sel['fd_len'] == len(f_sel['fd_sel_index'])
    
        if self.win == 0:
            f_sel["input_dim"] = 1
        else:
            f_sel["input_dim"] = len(self.data_loader.get_fd_names())
        f_sel["code_encoding"] = 2 # 1.onehot 2.标签编码
        f_sel["win"] = self.win
        f_sel["filter_win"] = self.filter_win
        f_sel["step"] = self.step

        if save_path is not None:
            with open(save_path, 'w') as save_path:
                json.dump(f_sel, save_path)
        return json.dumps(f_sel)

    
    # TODO: 优化数据处理，减少内存占用
    # 当数据太大时，会出现内存不足的情况
    # 1. 将数据集分成多个子集，分集训练模型
    # 2. 及时释放内存
    # 3. 增加对树形模型的支持：a.不需要归一化 b.分类特征数据仅需要标签编码
    # 4. 增加时间维度，hour，dayofweek
    # 5. 增加Conv2d模型的支持，增加range、min5，day等维度特征
    def setup_data(self, enable_cache: bool = False):
        """
        Base class DataHandler->load()->raw data(4个 DataFrame)
        """

        if not isinstance(self.data_loader, AHFDataLoader):
            raise ValueError("DataHandlerAHF init parameter data_loader is not AHFDataLoader")

        # 去重整序因子名称
        self._recover_factor_cols()

        # 0. 加载原始数据
        fd_dfs = self.data_loader.load(self.years)
        if not enable_cache:
            self.data_loader.clear_data()
        if self.start_date != "" and self.end_date !="" and self.start_date < self.end_date:
            start_date = int(pd.Timestamp(self.start_date).timestamp())
            end_date = int(pd.Timestamp(self.end_date).timestamp())
            for key, df in fd_dfs.items():
                fd_dfs[key] = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
                fd_dfs[key].sort_values(by=['code', 'date'], ascending=True, inplace=True)
                print(fd_dfs[key].shape)

                # 1. 清洗数据
                fd_dfs[key].fillna(0.0, inplace=True)

        for key, df in fd_dfs.items():
            if 'RSI_2' in fd_dfs[key].columns:
                # 对于涨跌幅过大的数据，过滤掉
                condition = (fd_dfs[key]['RSI_2'] != 0) & (fd_dfs[key]['change'] <= 7.0) & (fd_dfs[key]['change'] >= -7.0)
                fd_dfs[key] = fd_dfs[key][condition]
                print(fd_dfs[key].shape[0])

            fd_dfs[key].reset_index(drop=True, inplace=True)
            print("Clear after: ", fd_dfs[key].shape)

        if self.main_fd_name not in fd_dfs.keys():
            raise ValueError(f"main_fd_name {self.main_fd_name} not in fd_dfs keys {fd_dfs.keys()}")
        
        # 2. 导出特征数据筛选配置文件
        # sf_df['code_encoded'] = self.le.fit_transform(sf_df['code'].values)
        fut_codes_dict = {code: i for i, code in enumerate(MODEL_FUT_CODES)}

        sf_df = fd_dfs[self.main_fd_name]
        sf_df['code_encoded'] = sf_df['code'].apply(lambda x: fut_codes_dict[x])

        # 3. 生成标签
        sf_df['long_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.2 else 0)
        sf_df['short_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.2 else 0)
        if 'BAR_LENGTH_2' in sf_df.columns:
            sf_df['bar_length'] = sf_df['BAR_LENGTH_2']
        else:
            sf_df['bar_length'] = 0
        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 0 if x < -0.25 else 2 if x > 0.25 else 1)

        fd_dfs[self.main_fd_name] = sf_df

        # 4. 归一化
        col_names = self._get_factor_cols()
        if len(col_names) > 0:
            if self.is_normal:
                for key, df in fd_dfs.items():
                    if key == self.main_fd_name:
                        continue
                    df_mean = self.fd_means[key][col_names]
                    df_std = self.fd_stds[key][col_names]
                    partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                    df_standardized = df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                    df_standardized.fillna(0.0, inplace=True)
                    df_standardized.reset_index(drop=False, inplace=True)
                    fd_dfs[key][col_names] = df_standardized[col_names]
                    fd_dfs[key] = fd_dfs[key][['code', 'date'] + col_names]

        if len(col_names) > 0 and self.is_normal:
            df_mean = self.fd_means[key][col_names]
            df_std = self.fd_stds[key][col_names]
            partial_func = partial(self.standardize, means=df_mean, stds=df_std)
            df_standardized = fd_dfs[self.main_fd_name][col_names + ['code']].groupby('code')[col_names].apply(partial_func)
            df_standardized.fillna(0.0, inplace=True)
            df_standardized.reset_index(drop=False, inplace=True)
            fd_dfs[self.main_fd_name][col_names] = df_standardized[col_names]
        if len(col_names) > 0:
            fd_dfs[self.main_fd_name] = fd_dfs[self.main_fd_name][col_names + ['code', 'date', 'change', 'code_encoded', 'long_label', 'short_label', 'label', 'bar_length']]
        else:
            fd_dfs[self.main_fd_name] = fd_dfs[self.main_fd_name][['code', 'date', 'change', 'code_encoded', 'long_label', 'short_label', 'label', 'bar_length']]

        # 分箱处理change
        # step=2
        # midd=4
        # bins = [x/100.0 for x in range(-100, 100+step, step)]
        # bins = bins[:100//step-midd] + [-0.07, 0.07] + bins[-100//step+midd:]
        # # bins = [-np.inf] + bins + [np.inf]
        # sf_df['change_bins'] = pd.cut(sf_df['change'], bins=bins, labels=False)
        # sf_df['change_bins'].fillna(0, inplace=True)
        # sf_df['change_bins'] = sf_df['change_bins'].astype(np.int32)
        # sf_df['change'] = sf_df['change_bins'].apply(lambda x: bins[x])
        # sf_df.drop(['change_bins'], axis=1, inplace=True)

        # 5. 生成时间特征数据
        # 将date列由timestamp转换为东8区日期时间
        sf_df = fd_dfs[self.main_fd_name]
        tf_columns = []
        sf_df['date'] = pd.to_datetime(sf_df['date'], unit='s') + pd.Timedelta(hours=8)

        tf_columns = ['tf0', 'tf1', 'tf2', 'tf3', 'tf4']
        df_stamp= time_features(pd.to_datetime(sf_df['date'].values), freq='t').transpose(1, 0)
        # 'MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear'
        df_tf = pd.DataFrame(df_stamp, columns=tf_columns)
        sf_df = pd.concat([sf_df, df_tf], axis=1)

        # 6. 合并特征数据
        # if (len(lf_df) != 0 and len(lf_df) != len(sf_df)) or len(sf_df) != len(mf_df) or (len(sf_df) != len(ct_df) and len(ct_df) > 0):
        #     raise ValueError(f"lf_df {len(lf_df)}  sf_df {len(sf_df)}  mf_df {len(mf_df)}  ct_df {len(ct_df)}")

        # if self.verbose:
        #     print(sf_df[['code','date']].tail(5))
        #     if len(lf_df) > 0:
        #         print(lf_df[['code','date']].tail(5))
        #     if len(mf_df) > 0:
        #         print(mf_df[['code','date']].tail(5))
        #     if len(ct_df) > 0:
        #         print(ct_df[['code','date']].tail(5))
        lb_df = sf_df[['code', 'date', 'change', 'long_label', 'short_label', 'label', 'bar_length', 'code_encoded'] + tf_columns]
        assert len(lb_df) == len(sf_df)
        sf_df.drop(['code', 'date', 'change', 'code_encoded', 'long_label', 'short_label', 'bar_length', 'label'] + tf_columns, axis=1, inplace=True)
        fd_dfs[self.main_fd_name] = sf_df

        for key, df in fd_dfs.items():
            df.drop(['code', 'date'], axis=1, inplace=True)

        ft_df = pd.concat([lf_df, sf_df, mf_df], axis=1)
        self.feat_names = ft_df.columns.tolist()

        if self.verbose:
            # print(self.ins_nums)
            for key, df in fd_dfs.items():
                print(f"=== {key}: {df.shape} ===")

        self.ft_df = ft_df
        self.lb_df = lb_df

        if self.verbose:
            print(f"===============\nft{self.ft_df.columns.to_list()}\n================")
            print(f"===============\nlb{self.lb_df.shape} ft{self.ft_df.shape}\n================")
            print(self.ft_df)
            print(self.lb_df)
            print(self.lb_df['long_label'].value_counts(), lb_df['short_label'].value_counts())
            # print(self.lb_df['DAYOFWEEK'].value_counts())
            # print(self.lb_df['HOUR'].value_counts())

    def fetch(self, direct: str, win: int, filter_win: int):

        self.direct = direct
        self.win = win
        self.filter_win = filter_win
        if self.direct != 'ls' and self.direct != 'mls' and self.direct != 'long' and self.direct != 'short':
            raise ValueError(f"direct {self.direct} is not supported")
        
        if self.win == 0:
            raise ValueError(f"win {self.win} is not supported")

        # 生成模型输入数据配置文件
        self._dump_input_param_json()

        x_data = []
        y_data = []
        x_mark = []
        y_mark = []
        emb_data = []

        data1 = self.ft_df.astype(np.float32).values

        if self.direct == 'long':
            data2 = self.lb_df['long_label'].values
        elif self.direct == 'short':
            data2 = self.lb_df['short_label'].values
        elif self.direct == 'mls':
            data2 = self.lb_df['label'].values
        elif self.direct == 'ls':
            data2 = self.lb_df['change'].values
        else:
            raise ValueError(f"direct {self.direct} is not supported")
        
        if self.verbose:
            print(f"categorical column: {self.ct_cat_cols_names}")
            print(self.lb_df.tail(5))
            print(self.ft_df.tail(5))

        data3 = self.lb_df[['code_encoded'] + self.ct_cat_cols_names].astype(np.int32).values

        if self.timeenc == 0:
            data4 = self.lb_df[['tf0', 'tf1', 'tf2', 'tf3', 'tf4']].astype(np.int32).values
        elif self.timeenc == 1:
            data4 = self.lb_df[['tf0', 'tf1', 'tf2', 'tf3', 'tf4']].astype(np.float32).values
        print(f"===============\nfeatures: {data1.shape} label: {data2.shape} embedding: {data3.shape}\n================")

        filter_count = 0
        extreme_threshold = 3.0 # 过滤极端值的阈值
        if self.filter_win > 1: # 过滤采样，只用窗口内change的绝对值最大值的样本
            # 计算绝对值最大的行序号，目前固定滑动窗口
            rolling_max_index = self.lb_df['change'].rolling(self.filter_win).apply(lambda x: abs(x).idxmax())
            # 移动窗口的第一个值会返回 NaN，所以可以使用 fillna 方法填充为 0 或其他合适的值
            rolling_max_index = rolling_max_index.fillna(0)
            # 将结果转换为整数
            rolling_max_index = rolling_max_index.astype(int)

            for i in range(self.win, len(rolling_max_index)):
                if rolling_max_index[i] == self.lb_df.index[i]:
                    if self.win > 0 and data3[i][0] != data3[i - self.win][0]:
                        # print(i, data3[i], data3[i - self.win])
                        continue
                    # 筛选过于极端的值
                    if self.is_filter_extreme :
                        if (data1[i - self.win:i, 1] > extreme_threshold).any() or (data1[i - self.win:i, 1] < -1.0*extreme_threshold).any():
                            filter_count += 1
                            continue
                x_data.append(data1[i - self.win:i])
                y_data.append(data2[i])
                emb_data.append(data3[i - self.win:i].tolist())
                if self.timeenc is not None:
                    x_mark.append(data4[i - self.win:i])
                    y_mark.append(data4[i])
        else:
            # 防止跨code取值
            for i in range(self.win, len(data1), self.step):
                if self.win > 0 and data3[i][0] != data3[i - self.win][0]:
                    # print(i, data3[i], data3[i - self.win])
                    continue
                if self.is_filter_extreme: 
                    if (data1[i - self.win:i, 1] > extreme_threshold).any() or (data1[i - self.win:i, 1] < -1.0*extreme_threshold).any():
                        filter_count += 1
                        continue

                x_data.append(data1[i - self.win:i])
                y_data.append(data2[i])
                emb_data.append(data3[i - self.win:i].tolist())
                if self.timeenc is not None:
                    x_mark.append(data4[i - self.win:i])
                    y_mark.append(data4[i])

        del data1, data2, data3
        if self.timeenc is not None:
            del data4

        print("******************************")
        print(f"label: {round(sum(y_data) / len(y_data) * 100, 3)}%, total: {len(y_data)} filter win: {self.filter_win} {self.is_filter_extreme} filter: {filter_count} is_normal: {self.is_normal}")
        print("******************************")
        # encoded_data = np.array(encoded_data)
        # if encoded_data.shape[-1] == 1:
        #     encoded_data = np.squeeze(encoded_data, axis=-1)
        if self.timeenc is not None:
            return np.array(emb_data), np.array(x_data), np.array(y_data), x_mark, y_mark
        else:
            return np.array(emb_data), np.array(x_data), np.array(y_data)

 
