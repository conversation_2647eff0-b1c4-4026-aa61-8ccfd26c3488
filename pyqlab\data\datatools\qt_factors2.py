from datetime import datetime, date
import time
import pytz
import pandas as pd
import sys
import numpy as np
import json
from argparse import ArgumentParser

# sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

# ### FactorKvDB

class FactorsKvDB():
    def __init__(self,
        # dbfile="e:/featdata/kv.db",
        key_prefix='ffs:', #fsfs 股指期货因子 ffs 商品期货因子
        years=[],
        dbfile="d:/RoboQuant2/store/kv.db",
        save_path="e:/featdata",
        save_file="",
        batch_size=50000,
    ) -> None:
        self.years = years
        self._dbfile=dbfile
        self._save_path = save_path
        self._save_file = save_file
        self._batch_size = batch_size
        self._db=None
        self._key_prefix=key_prefix
        self._keys=[]
        self._ls_col_names=[]
        self._ct_col_names=[]
        self._tz=pytz.timezone('Asia/Shanghai')
        self._is_first_batch = {}  # 跟踪每个年份是否是第一批次

    def open_db(self, mode):
        if self._db:
            self.close_db()
        
        try:
            self._db=create_db("leveldb", self._dbfile, mode)
        except:
            raise 'Fail to open db!'
        self._ls_col_names, self._ct_col_names = self.get_factors_colnames()

    def close_db(self):
        if not self._db:
            raise "not db open."
        self._db.close()
        del self._db
        self._db=None

    def load_all_keys(self):
        if not self._db:
            raise "first open a db."
        self._keys.clear()
        cursor = self._db.new_cursor()
        while cursor.valid():
            key = cursor.key().decode('gbk', 'ignore')
            if len(key) >= 16 and key[0:len(self._key_prefix)] == self._key_prefix: #'ffs:'
                self._keys.append(key)
            cursor.next()
        del cursor

    def get_all_labels(self)->set:
        lbs = set()
        if len(self._keys) == 0:
            self.load_all_keys()
            
        for key in self._keys:
            pos0=key.find(':')
            pos1=key.find(':', pos0+1)
            lb=key[pos0+1:pos1]
            lbs.add(lb)
        return lbs

    def get_factors_colnames(self):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        ls_colnames = []
        ct_colnames = []
        while cursor.valid():
            key = cursor.key()
            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:
                value = cursor.value().decode()
                s2 = value.split('|')
                if len(s2) <= 2:
                    cursor.next()
                    continue
                fds = dict(json.loads(s2[0]))
                for key, value in fds.items():
                    if key == "label":
                        continue
                    ls_colnames.append('code')
                    ls_colnames.append('date')
                    for k, v in value.items():
                        if isinstance(v, list):
                            ls_colnames.append(f'{k}_1')
                            ls_colnames.append(f'{k}_2')
                        else:
                            ls_colnames.append(f'{k}')
                    ls_colnames.append('change')
                    break
                if len(s2) >= 2:
                    ct_colnames.append('code')
                    ct_colnames.append('date')
                    ct = json.loads(s2[len(s2) - 2])
                    for k, v in ct.items():
                        if isinstance(v, list):
                            ct_colnames.append(f'{k}_1')
                            ct_colnames.append(f'{k}_2')
                        else:
                            ct_colnames.append(f'{k}')
                del cursor
                return ls_colnames, ct_colnames
            cursor.next()
        return ls_colnames, ct_colnames

    def get_all_factors(self, year: int, batch_size=10000, save_batch=True):
        """
        获取指定年份的所有因子数据，支持分批处理和保存
        
        参数:
            year: 要获取的年份
            batch_size: 每批处理的记录数
            save_batch: 是否分批保存
        
        返回:
            tuple: (因子字典, 其他数据)
        """
        if not self._db:
            raise ValueError("请先打开数据库")
        if len(self._ls_col_names) == 0 or len(self._ct_col_names) == 0:
            raise ValueError("请先获取因子列名")

        batch_count = 0
        total_count = 0
        fds = {}
        cdata = []
        cursor = self._db.new_cursor()

        # 初始化该年份的第一批次标志
        self._is_first_batch[year] = True

        print(f"开始处理 {year} 年数据...")
        
        while cursor.valid():
            key = cursor.key()
            if len(key) >= 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:
                value = cursor.value().decode()
                s1 = key.decode().split(':')
                dt = date.fromtimestamp(int(s1[2]))
                if dt.year != year:
                    cursor.next()
                    continue
                
                s2 = value.split('|')
                assert len(s2) >= 3, f'invalid value: {value}'
                fd = dict(json.loads(s2[0]))
                ct = json.loads(s2[len(s2) - 2])
                
                for key, val in fd.items():
                    if key == "label":
                        continue
                    if key not in fds:
                        fds[key] = []
                    litem = []
                    litem.append(s1[1])
                    litem.append(int(s1[2]))
                    for k, v in val.items():
                        if isinstance(v, list):
                            litem.append(v[1])
                            litem.append(v[2])
                        else:
                            litem.append(v)
                    litem.append(float(s2[len(s2)-1])) # change
                    fds[key].append(litem)
                
                citem = []
                citem.append(s1[1])
                citem.append(int(s1[2]))
                for _, v in ct.items():
                    if isinstance(v, list):
                        citem.append(v[1])
                        citem.append(v[2])
                    else:
                        citem.append(v)
                cdata.append(citem)
                
                batch_count += 1
                total_count += 1
                
                # 达到批处理大小时保存
                if save_batch and batch_count >= batch_size:
                    self._export_batch(fds, cdata, year)
                    print(f"已处理并保存 {total_count} 条记录")
                    # 标记不再是第一批次
                    self._is_first_batch[year] = False
                    # 清空当前批次数据
                    batch_count = 0
                    fds = {}
                    cdata = []

                    # 释放内存
                    import gc
                    gc.collect()

            cursor.next()
        
        # 保存最后一批数据
        if save_batch and batch_count > 0:
            self._export_batch(fds, cdata, year)
            print(f"已处理并保存最后 {batch_count} 条记录，总计 {total_count} 条")
        
        del cursor
        print(f"完成处理 {year} 年数据，共 {total_count} 条记录")
        
        return fds, cdata

    def _export_batch(self, fds, cdata, year):
        """
        导出一批数据到文件，支持批次合并

        参数:
            fds: 因子数据字典
            cdata: 其他数据
            year: 年份
        """
        # 创建保存目录
        import os
        # save_dir = os.path.join(self._save_path, str(year))
        os.makedirs(self._save_path, exist_ok=True)

        is_first_batch = self._is_first_batch.get(year, True)

        # 保存因子数据
        for factor_name, data in fds.items():
            if len(data) > 0:
                new_df = pd.DataFrame(data, columns=self._ls_col_names)
                file_name = f"{self._key_prefix}_{factor_name}.{self._save_file}.{year}.parquet"
                file_path = os.path.join(self._save_path, file_name)

                if is_first_batch:
                    # 第一批次：检查文件是否存在，如果存在则删除
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"已清除旧文件: {file_path}")
                    # 直接保存新数据
                    new_df.to_parquet(file_path, index=False, compression='snappy')
                    print(f"第一批次保存: {file_path}, 记录数: {len(new_df)}")
                else:
                    # 后续批次：读取已有文件并合并
                    if os.path.exists(file_path):
                        existing_df = pd.read_parquet(file_path)
                        merged_df = pd.concat([existing_df, new_df], axis=0, ignore_index=True)
                        merged_df.to_parquet(file_path, index=False, compression='snappy')
                        print(f"合并保存: {file_path}, 原有: {len(existing_df)}, 新增: {len(new_df)}, 总计: {len(merged_df)}")
                    else:
                        # 如果文件不存在，直接保存
                        new_df.to_parquet(file_path, index=False, compression='snappy')
                        print(f"新建保存: {file_path}, 记录数: {len(new_df)}")

        # 保存其他数据
        if len(cdata) > 0:
            ct_df = pd.DataFrame(cdata, columns=self._ct_col_names)
            ct_file = os.path.join(self._save_path, f"ffs_ct.{self._save_file}.{year}.parquet")

            if is_first_batch:
                # 第一批次：检查文件是否存在，如果存在则删除
                if os.path.exists(ct_file):
                    os.remove(ct_file)
                    print(f"已清除旧文件: {ct_file}")
                # 直接保存新数据
                ct_df.to_parquet(ct_file, index=False, compression='snappy')
                print(f"第一批次保存: {ct_file}, 记录数: {len(ct_df)}")
            else:
                # 后续批次：读取已有文件并合并
                if os.path.exists(ct_file):
                    existing_ct_df = pd.read_parquet(ct_file)
                    merged_ct_df = pd.concat([existing_ct_df, ct_df], axis=0, ignore_index=True)
                    merged_ct_df.to_parquet(ct_file, index=False, compression='snappy')
                    print(f"合并保存: {ct_file}, 原有: {len(existing_ct_df)}, 新增: {len(ct_df)}, 总计: {len(merged_ct_df)}")
                else:
                    # 如果文件不存在，直接保存
                    ct_df.to_parquet(ct_file, index=False, compression='snappy')
                    print(f"新建保存: {ct_file}, 记录数: {len(ct_df)}")
    

    def write(self, key, value):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.put(key, value)
        transaction.commit()
        del transaction
        
    def delete(self, key):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.delete(key)
        transaction.commit()
        del transaction

    def clear(self):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        cursor = self._db.new_cursor()
        while cursor.valid():
            key = cursor.key()
            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:
                transaction.delete(key)
                print(f'del key: {key}')
            cursor.next()
        transaction.commit()
        del transaction
        del cursor

    def query(self):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        while cursor.valid():
            print(cursor.key())
            # print(cursor.key(), cursor.value())
            cursor.next()
        del cursor

    def export_all(self):
        def trans_timestamp(dt):
            # return int(time.mktime(dt.timetuple()))//300
            return int(dt//300)

        def log_return(series):
            return np.log(series).diff()
            
        if not self._db:
            raise "first open a db."
        # self.open_db(Mode.read)
        df=self.read_all()
        # self.close_db()
        print(df.shape)

        try:
            cols=df.columns
            df = df.groupby(["label", "datetime"]).agg({"mean"}).reset_index()
            df.columns = cols
            df["time_id"]=df["datetime"].apply(trans_timestamp)

            for lb in df["label"].unique():
                # df.to_parquet(f"../data/tickdata.parquet", engine='fastparquet')
                df2=df[df["label"]==lb]
                df2.to_parquet(f"{self._save_path}/tickdata.{lb}.parquet", engine='fastparquet')
                # df = pd.read_parquet(f"../data/tickdata.parquet")
                df2 = df2.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()
                df2['return'] = log_return(df2['price'])
                df2=df2.fillna(0)
                df2['target'] = (df2['return']>0).astype(int)
                df2=df2.drop(['price', 'return'], axis=1)
                df2.to_parquet(f"{self._save_path}/tickdata_target.{lb}.parquet", engine='fastparquet')
        except:
            raise 'Fail to export all data!'

    def export_to_file(self):
        if not self._db:
            self.open_db(Mode.read)
        for year in self.years:
            fds, cxt = self.get_all_factors(year, self._batch_size, True)
            if len(fds) == 0 or len(cxt) == 0:
                print(f'empty {year}')
                continue
            for key, value in fds.items():
                df = pd.DataFrame(value, columns=self._ls_col_names)
                print(year, key, df.shape)
                df.to_parquet(f"{self._save_path}/{self._key_prefix}_{key}.{self._save_file}.{year}.parquet", engine='fastparquet')
            cdf = pd.DataFrame(cxt, columns=self._ct_col_names)
            print(year, "ct", cdf.shape)
            cdf.to_parquet(f"{self._save_path}/{self._key_prefix}_ct.{self._save_file}.{year}.parquet", engine='fastparquet')
        self.close_db()
           
def merge_data():
    # ### 合并
    # 将按月份导出的数据合并成一个文件
    dtype = ['lf', 'sf', 'mf', 'ct']
    for dt in dtype:
        df = pd.read_parquet(f'e:/featdata/ffs_{dt}.main.2023.parquet')
        dfa = pd.read_parquet(f'e:/featdata/ffs_{dt}.main.12.2023.parquet')
        print(df.shape, dfa.shape)
        df = pd.concat([df,dfa],axis=0)
        print(df.shape)
        df = df.drop_duplicates(subset=['code', 'date'], keep='last')
        print(df.shape)
        df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
        df.reset_index(drop=True, inplace=True)
        df.to_parquet(f'e:/featdata/ffs_{dt}.main.2023.A.parquet', engine='fastparquet')


def main(args):

    print(args)
    assert args.is_sf and args.key_prefix == "fsfs" or \
        not args.is_sf and args.key_prefix == "ffs", "key_prefix error."

    ff = FactorsKvDB(key_prefix=args.key_prefix, # ffs: or fsfs:
                    years=args.years,
                    dbfile=args.dbfile,
                    save_path=args.save_path,
                    save_file=args.save_file,
                    batch_size=args.batch_size,
                    )
    ff.export_to_file()
    # ff.export_to_file_by_single()




if __name__ == '__main__':
    parser = ArgumentParser()

    # parser.add_argument('--is_sf', action='store_true', default=False)
    # parser.add_argument('--dbfile', type=str, default="d:/RoboQuant2/store/kv.db")
    # parser.add_argument('--key_prefix', type=str, default="ffs:", choices=['ffs:', 'fsfs:'])
    # parser.add_argument('--save_path', type=str, default="f:/featdata/top")
    # parser.add_argument('--save_file', type=str, default="top")
    # parser.add_argument('--years', type=list, default=[2024, 2025])

    parser.add_argument('--is_sf', action='store_true', default=False)
    parser.add_argument('--dbfile', type=str, default="e:/lab/RoboQuant/bin/x64/Release/store/kv.db")
    parser.add_argument('--key_prefix', type=str, default="ffs", choices=['ffs', 'fsfs'])
    parser.add_argument('--save_path', type=str, default="F:/featdata/tmp")
    parser.add_argument('--save_file', type=str, default="top")
    parser.add_argument('--batch_size', type=int, default=10000)
    parser.add_argument('--years', type=list, default=[2025])

    args = parser.parse_args()

    main(args)

    # merge_data()

