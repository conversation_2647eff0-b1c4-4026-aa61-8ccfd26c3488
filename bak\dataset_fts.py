from typing import Union, List, Tuple, Dict, Text, Optional
import pandas as pd
import numpy as np
from pprint import pprint
import torch
from inspect import getfullargspec
from torch.utils.data import Dataset
from pyqlab.data import DataHandler

# device = "cuda" if torch.cuda.is_available() else "cpu"

# def _to_tensor(x):
#     if not isinstance(x, torch.Tensor):
#         return torch.tensor(x, dtype=torch.float, device=device)
#     return x

"""
Factor Time Series Dataset
提供两种模式：
1. 以MLP,CNN,TCN等模型的输入输出模式
2. 以Transformer,GPT等模型的输入输出模式
"""

class FTSDataset(Dataset):
    """
    (A)icm (H)istory (F)actor Dataset
    """
    def __init__(
        self,
        handler: Union[Dict, DataHandler],
        model_type=None,
        seq_len=20,
        label_len=0,
        pred_len=1,
        **kwargs
    ):
        super().__init__()
        self.model_type = model_type
        self.seq_len = seq_len
        self.label_len = label_len
        if self.model_type == 0:
            self.pred_len = 0
        else:
            self.pred_len = pred_len
        print(f"FTSDataset model_type: {self.model_type}, seq_len: {self.seq_len}, label_len: {self.label_len}, pred_len: {self.pred_len}")
        self.lb_df = pd.DataFrame()
        self.ft_df = pd.DataFrame()
        self.tg_df = pd.DataFrame()
        self.tm_df = pd.DataFrame()

        self.fetch_kwargs = {}
        self.handler = handler

    def config(self, handler_kwargs: dict = None, **kwargs):
        if handler_kwargs is not None:
            self.handler.config(**handler_kwargs)        

    def setup_data(self, handler_kwargs: dict = None, **kwargs):
        if handler_kwargs is not None:
            self.handler.config(**handler_kwargs)
            self.handler.setup_data()

    def get_ins_nums(self):
        return self.handler._get_ins_nums()
    
    def save_model_inputs_config(self, save_path: str):
        self.handler._dump_input_param_json(
            save_path,
            self.model_type,
            self.seq_len,
            self.label_len,
            self.pred_len
            )

    def prepare(
        self,
        **kwargs
    ) -> Union[List[pd.DataFrame], pd.DataFrame]:
        """
        dataset using must call this function to call->fetch() load data
        """
        print(f"FTSDataset parameter: {kwargs}")

        return self.handler.fetch(**kwargs)
    
    def load_data(self):
        self.lb_df, self.ft_df = self.handler.lb_df, self.handler.ft_df
        if self.model_type == 1:
            self.ft_df['bar_length'] = self.handler.lb_df['bar_length'] 
        
        # 统计每个合约的数据量
        codecount=self.lb_df['code_encoded'].value_counts().to_dict()
        codecount = [i for i in dict(sorted(codecount.items())).values()]
        codecount = [i - self.seq_len - self.pred_len for i in codecount]
        # 每个值都必须大于等于0
        assert all([i >= 0 for i in codecount])
        self.codecount = np.cumsum(codecount)
        print(f"Dataset size: {self.codecount[-1]}")
        print(self.lb_df)
        print(self.lb_df['change'].describe())

    def __i_to_idx(self, i):
        if i < 0:
            i = self.codecount[-1] + i
        n = np.searchsorted(self.codecount, i, side='right')
        return i + n * (self.seq_len + self.pred_len)
    
    def __len__(self):
        return self.codecount[-1]

    def __getitem__(self, i):
        idx = self.__i_to_idx(i)
        if self.model_type == 0:
            emb = self.lb_df[['code_encoded'] + self.handler.ct_cat_cols_names].iloc[idx:idx+self.seq_len, :].astype(np.int32).values # code & market factors embedding
            assert emb[0, 0] == emb[-1, 0], f"code must be the same in the sequence: {emb[0, 0]} != {emb[-1, 0]}"
            x = self.ft_df.iloc[idx:idx+self.seq_len, :].astype(np.float32).values # factors
            y = self.lb_df[["change"]].iloc[idx:idx+self.seq_len, :].astype(np.float32).values[-1:,:] # change to label
            return torch.tensor(emb, dtype=torch.int32), torch.tensor(x, dtype=torch.float32), torch.tensor(y, dtype=torch.float32).squeeze()
        elif self.model_type == 1:
            s_begin = idx
            s_end = s_begin + self.seq_len
            r_begin = s_end - self.label_len
            r_end = r_begin + self.label_len + self.pred_len
            emb = self.lb_df[['code_encoded'] + self.handler.ct_cat_cols_names].iloc[s_begin:s_end, :].astype(np.int32).values
            assert emb[0, 0] == emb[-1, 0], f"code must be the same in the sequence: {emb[0, 0]} != {emb[-1, 0]}"
            x_data = self.ft_df.iloc[s_begin:s_end, :].astype(np.float32).values
            y_data = self.ft_df.iloc[r_begin:r_end, :].astype(np.float32).values
            if self.handler.timeenc == 0:
                tf = self.lb_df[['tf0', 'tf1', 'tf2', 'tf3', 'tf4']].iloc[idx:idx+self.seq_len+self.pred_len, :].astype(np.int32).values
                x_mark = torch.tensor(tf[:-self.pred_len], dtype=torch.int32)
                y_mark = torch.tensor(tf[-(self.pred_len+self.label_len):], dtype=torch.int32)
            elif self.handler.timeenc == 1:
                tf = self.lb_df[['tf0', 'tf1', 'tf2', 'tf3', 'tf4']].iloc[idx:idx+self.seq_len+self.pred_len, :].astype(np.float32).values
                x_mark = torch.tensor(tf[:-self.pred_len], dtype=torch.float32)
                y_mark = torch.tensor(tf[-(self.pred_len+self.label_len):], dtype=torch.float32)
            else:
                raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")
            emb = torch.tensor(emb, dtype=torch.int32)
            x_data = torch.tensor(x_data, dtype=torch.float32)
            y_data = torch.tensor(y_data, dtype=torch.float32)
            return emb, x_data, x_mark, y_data, y_mark
        else:
            raise ValueError("model_type must be 0 or 1")
    