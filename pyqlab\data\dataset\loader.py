# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# coding=utf-8
"""
Loader里，主要功能是加载原始数据到内存，不做太多的处理
"""
# import abc
# import warnings
import pandas as pd
import numpy as np
# import datetime
# import json
import os

# from pyqlab.const import MAIN_SEL_FUT_CODES

class AHFDataLoader():
    '''
    (A)icm (H)istory (F)actor Data Loader

    '''
    def __init__(
           self,
           data_path = '',
           train_codes=[],
           years=['2024', '2025'],
           fd_set = set((1,0), (1,1), (2,0), (2,1), (6,0), (6,1))
       ) -> None:
            self.data_path = data_path
            self.train_codes = train_codes
            self.years = years # 加载数据的年份列表
            self.fd_set = fd_set # 加载数据的因子列表，默认是(1,0), (1,1), (2,0), (2,1), (6,0), (6,1) 6个因子
            self.fd_dfs = {}
            self.interface_params = {
                'input_dim': 2, # 1: expression call 2: API call
                'code_encoding': 0, # 0:unsing, 1:onehot, 2:embedding
            }
            super().__init__()


    def clear_data(self):
        for _, df in self.fd_dfs.items():
            df.clear()

    def _load_data_from_file(self):
        for fd in self.fd_set:
            for year in self.years:
                key = f'fd_{fd[0]}_{fd[1]}'
                if key not in self.fd_dfs:
                    self.fd_dfs[key] = pd.DataFrame()
                print(f"Loading {year} {key} data...")
                data_file = f'{self.data_path}/ffs_{key}.{year}.parquet'
                if os.path.isfile(data_file):
                    self.fd_dfs[key] = pd.concat([self.fd_dfs[key], pd.read_parquet(data_file, compression='snappy')])
        
    def _load(self):
        # self.fd_dfs = self.fd.get_pf_data(self.years)
        pass

    def _prepare_data(self):
        self._load_data_from_file()
        assert len(self.fd_dfs) > 0, "load raw factor features data is empty."

        # 清除不需要的数据
        if len(self.train_codes) > 0:
            for key, df in self.fd_dfs.items():
                self.fd_dfs[key] = df[df['code'].isin(self.train_codes)] 

        for key, df in self.fd_dfs.items():
            # todo: 最好是分code处理，在两个code衔接处分组时会发生跨代码的问题
            if self.fd_dfs[key].empty:
                continue
            self.fd_dfs[key].sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.fd_dfs[key]['change'] = self.fd_dfs[key]['change'].astype(np.float32)
            # 将当前的涨跌幅移动后5行，作为当前的label
            self.fd_dfs[key]['change'] = self.fd_dfs[key]['change'].shift(-5)
            self.fd_dfs[key] = self.fd_dfs[key][:-5]

    def transform(self):
        self._prepare_data()
        # self._label_encode()
        # self._get_folds()
        # self._dump_input_param_json()
        return self.fd_dfs

    def load(self, years) -> pd.DataFrame:
        self.years=years
        return self.transform()
    
    def get_fd_names(self):
        return self.fd_dfs.keys()
        